<template>
  <div class="system-monitoring-dashboard">
    <div class="monitoring-header">
      <h3>System State Monitoring</h3>
      <div class="monitoring-controls">
        <button 
          v-if="!isMonitoringActive" 
          @click="startMonitoring"
          class="btn role-primary"
        >
          Start Monitoring
        </button>
        <button 
          v-else 
          @click="stopMonitoring"
          class="btn role-secondary"
        >
          Stop Monitoring
        </button>
        <button 
          @click="refreshState"
          class="btn role-tertiary"
          :disabled="isLoading"
        >
          <i v-if="isLoading" class="icon icon-spinner icon-spin" />
          <i v-else class="icon icon-refresh" />
          Refresh
        </button>
      </div>
    </div>

    <div class="monitoring-status">
      <div class="status-card" :class="{ active: isMonitoringActive }">
        <div class="status-indicator">
          <i :class="statusIcon" />
        </div>
        <div class="status-info">
          <h4>Monitoring Status</h4>
          <p>{{ monitoringStatus.isActive ? 'Active' : 'Inactive' }}</p>
          <small v-if="monitoringStatus.lastHealthCheck">
            Last check: {{ formatDate(monitoringStatus.lastHealthCheck) }}
          </small>
        </div>
      </div>

      <div class="status-card">
        <div class="status-indicator">
          <i class="icon icon-cluster" />
        </div>
        <div class="status-info">
          <h4>Resources</h4>
          <div class="resource-counts">
            <span>Clusters: {{ healthSummary.resources.clusters }}</span>
            <span>Credentials: {{ healthSummary.resources.credentials }}</span>
            <span>Users: {{ healthSummary.resources.users }}</span>
            <span>Apps: {{ healthSummary.resources.applications }}</span>
          </div>
        </div>
      </div>

      <div class="status-card">
        <div class="status-indicator">
          <i class="icon icon-compass" />
        </div>
        <div class="status-info">
          <h4>Journeys</h4>
          <div class="journey-info">
            <span>Available: {{ healthSummary.journeys.available }}</span>
            <span>Current: {{ healthSummary.journeys.current || 'None' }}</span>
            <span>Enabled: {{ healthSummary.journeys.enabled ? 'Yes' : 'No' }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-if="monitoringStatus.errorCount > 0" class="error-summary">
      <h4>Recent Errors ({{ monitoringStatus.errorCount }})</h4>
      <div class="error-list">
        <div v-for="error in recentErrors" :key="error.timestamp" class="error-item">
          <span class="error-time">{{ formatDate(error.timestamp) }}</span>
          <span class="error-message">{{ error.message }}</span>
        </div>
      </div>
    </div>

    <div class="system-state-details">
      <h4>System State Details</h4>
      <div class="state-grid">
        <div class="state-item">
          <label>Last Updated:</label>
          <span>{{ systemState.lastUpdated ? formatDate(systemState.lastUpdated) : 'Never' }}</span>
        </div>
        <div class="state-item">
          <label>Last Resource Change:</label>
          <span v-if="systemState.lastResourceChange">
            {{ systemState.lastResourceChange.resourceType }} - {{ systemState.lastResourceChange.event }}
            ({{ formatDate(systemState.lastResourceChange.timestamp) }})
          </span>
          <span v-else>None</span>
        </div>
        <div class="state-item">
          <label>Monitoring Errors:</label>
          <span>{{ monitoringStatus.errorCount }}</span>
        </div>
        <div class="state-item">
          <label>Active Watchers:</label>
          <span>{{ monitoringStatus.watcherCount }}</span>
        </div>
      </div>
    </div>

    <div v-if="isDebugMode" class="debug-info">
      <h4>Debug Information</h4>
      <pre>{{ JSON.stringify({ systemState, monitoringStatus, healthSummary }, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'SystemMonitoringDashboard',

  data() {
    return {
      isLoading: false,
    };
  },

  computed: {
    ...mapGetters('journeys', [
      'getSystemState',
      'isSystemMonitoringActive',
      'getSystemMonitoringStatus',
      'getSystemHealthSummary',
      'isDebugMode',
      'getErrors',
    ]),

    systemState() {
      return this.getSystemState;
    },

    isMonitoringActive() {
      return this.isSystemMonitoringActive;
    },

    monitoringStatus() {
      return this.getSystemMonitoringStatus;
    },

    healthSummary() {
      return this.getSystemHealthSummary;
    },

    statusIcon() {
      if (this.isMonitoringActive) {
        return 'icon icon-checkmark text-success';
      }
      return 'icon icon-warning text-warning';
    },

    recentErrors() {
      return this.getErrors.slice(-5); // Show last 5 errors
    },
  },

  methods: {
    ...mapActions('journeys', [
      'startSystemStateMonitoring',
      'stopSystemStateMonitoring',
      'updateSystemStateFromRancher',
      'performSystemHealthCheck',
    ]),

    async startMonitoring() {
      try {
        this.isLoading = true;
        await this.startSystemStateMonitoring();
        this.$store.dispatch('growl/success', {
          title: 'System Monitoring Started',
          message: 'System state monitoring is now active',
        });
      } catch (error) {
        this.$store.dispatch('growl/error', {
          title: 'Failed to Start Monitoring',
          message: error.message,
        });
      } finally {
        this.isLoading = false;
      }
    },

    async stopMonitoring() {
      try {
        this.isLoading = true;
        await this.stopSystemStateMonitoring();
        this.$store.dispatch('growl/success', {
          title: 'System Monitoring Stopped',
          message: 'System state monitoring has been stopped',
        });
      } catch (error) {
        this.$store.dispatch('growl/error', {
          title: 'Failed to Stop Monitoring',
          message: error.message,
        });
      } finally {
        this.isLoading = false;
      }
    },

    async refreshState() {
      try {
        this.isLoading = true;
        await this.updateSystemStateFromRancher();
        await this.performSystemHealthCheck();
        this.$store.dispatch('growl/success', {
          title: 'State Refreshed',
          message: 'System state has been updated',
        });
      } catch (error) {
        this.$store.dispatch('growl/error', {
          title: 'Failed to Refresh State',
          message: error.message,
        });
      } finally {
        this.isLoading = false;
      }
    },

    formatDate(date) {
      if (!date) return 'Never';
      return new Date(date).toLocaleString();
    },
  },

  mounted() {
    // Auto-start monitoring if enabled in config
    if (this.$store.getters['journeys/isJourneysEnabled'] && !this.isMonitoringActive) {
      this.startMonitoring();
    }
  },

  beforeDestroy() {
    // Clean up monitoring when component is destroyed
    if (this.isMonitoringActive) {
      this.stopMonitoring();
    }
  },
};
</script>

<style lang="scss" scoped>
.system-monitoring-dashboard {
  padding: 20px;
  
  .monitoring-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
    }
    
    .monitoring-controls {
      display: flex;
      gap: 10px;
    }
  }
  
  .monitoring-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    
    .status-card {
      display: flex;
      align-items: center;
      padding: 15px;
      border: 1px solid var(--border);
      border-radius: 4px;
      background: var(--body-bg);
      
      &.active {
        border-color: var(--success);
        background: var(--success-bg);
      }
      
      .status-indicator {
        margin-right: 15px;
        font-size: 24px;
      }
      
      .status-info {
        flex: 1;
        
        h4 {
          margin: 0 0 5px 0;
          font-size: 14px;
          font-weight: 600;
        }
        
        p {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }
        
        small {
          color: var(--muted);
        }
        
        .resource-counts,
        .journey-info {
          display: flex;
          flex-direction: column;
          gap: 2px;
          
          span {
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .error-summary {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid var(--error);
    border-radius: 4px;
    background: var(--error-bg);
    
    h4 {
      margin: 0 0 10px 0;
      color: var(--error);
    }
    
    .error-list {
      .error-item {
        display: flex;
        gap: 10px;
        margin-bottom: 5px;
        font-size: 12px;
        
        .error-time {
          color: var(--muted);
          min-width: 120px;
        }
        
        .error-message {
          flex: 1;
        }
      }
    }
  }
  
  .system-state-details {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 10px 0;
    }
    
    .state-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 10px;
      
      .state-item {
        display: flex;
        justify-content: space-between;
        padding: 8px;
        border: 1px solid var(--border);
        border-radius: 4px;
        font-size: 12px;
        
        label {
          font-weight: 600;
        }
      }
    }
  }
  
  .debug-info {
    pre {
      background: var(--body-bg);
      border: 1px solid var(--border);
      border-radius: 4px;
      padding: 10px;
      font-size: 10px;
      overflow: auto;
      max-height: 300px;
    }
  }
}
</style>
