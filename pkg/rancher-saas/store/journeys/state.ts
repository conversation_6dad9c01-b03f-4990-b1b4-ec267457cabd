import type { JourneyState } from '../../types/journey';

export default function(): JourneyState {
  return {
    // Journey definitions loaded from configuration
    definitions: {},
    
    // User progress tracking across all journeys
    userProgress: {},
    
    // Current active journey state
    currentJourney: null,
    currentProgress: null,
    
    // Journey history for analytics and resumption
    journeyHistory: [],
    
    // System state for triggering journeys
    systemState: {
      clusters: [],
      credentials: [],
      users: [],
      applications: [],
      lastLogin: null,
      isFirstLogin: false,
      hasCompletedOnboarding: false,
      activeFeatures: [],
      userRole: null,
      clusterCount: 0,
      credentialCount: 0,
      applicationCount: 0,
      userCount: 0,
      lastUpdated: null,
      lastResourceChange: null,
      healthStatus: null,
    },

    // System monitoring state
    systemMonitoring: {
      isActive: false,
      healthCheckInterval: null,
      lastHealthCheck: null,
      watcherIds: [],
      errorCount: 0,
    },
    
    // Journey system operational state
    isActive: false,
    isLoading: false,
    
    // Pending actions and decision tracking
    pendingActions: [],
    decisionPoints: {},
    
    // Journey system configuration
    config: {
      enableJourneys: true,
      enableAnalytics: true,
      debugMode: false,
      autoTrigger: true,
      persistProgress: true,
      maxConcurrentJourneys: 1,
      journeyTimeout: 30 * 60 * 1000, // 30 minutes
      retryAttempts: 3,
      analyticsEndpoint: null,
    },
    
    // Error tracking
    errors: [],
    lastError: null,
    
    // Performance and analytics data
    analytics: {
      journeyStartCount: {},
      journeyCompletionCount: {},
      journeySkipCount: {},
      averageCompletionTime: {},
      dropOffPoints: {},
      popularChoices: {},
      userPaths: [],
    },
    
    // Cache for performance
    cache: {
      availableJourneys: null,
      systemStateHash: null,
      lastCacheUpdate: null,
      cacheTimeout: 5 * 60 * 1000, // 5 minutes
    },
    
    // Journey triggers and conditions
    triggers: {
      navigationTriggers: {},
      eventTriggers: {},
      timeTriggers: {},
      conditionTriggers: {},
    },
    
    // UI state
    ui: {
      showJourneyOverlay: false,
      overlayPosition: 'center',
      minimized: false,
      lastInteraction: null,
      tourMode: false, // vs PLG mode
    }
  };
}
