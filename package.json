{"name": "rancher-saas", "version": "0.1.0", "private": false, "engines": {"node": ">=20"}, "dependencies": {"@rancher/shell": "^3.0.4", "posthog-js": "^1.236.1"}, "resolutions": {"@types/node": "~20.10.0", "@types/lodash": "4.17.5"}, "scripts": {"dev": "NODE_ENV=dev ./node_modules/.bin/vue-cli-service serve", "build": "./node_modules/.bin/vue-cli-service build", "clean": "./node_modules/@rancher/shell/scripts/clean", "build-pkg": "./node_modules/@rancher/shell/scripts/build-pkg.sh", "serve-pkgs": "./node_modules/@rancher/shell/scripts/serve-pkgs", "publish-pkgs": "./node_modules/@rancher/shell/scripts/extension/publish", "parse-tag-name": "./node_modules/@rancher/shell/scripts/extension/parse-tag-name"}}