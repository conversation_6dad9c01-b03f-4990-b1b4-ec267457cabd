import Dispatcher from "./dispatcher";

export declare class Redirect<PERSON><PERSON>ler implements Di<PERSON>atcher.DispatchHandlers{
  constructor (dispatch: Dispatcher, maxRedirections: number, opts: Dispatcher.DispatchOptions, handler: Dispatcher.DispatchHandlers)
}

export declare class DecoratorHandler implements Dispatcher.DispatchHandlers{
  constructor (handler: Dispatcher.DispatchHandlers)
}
