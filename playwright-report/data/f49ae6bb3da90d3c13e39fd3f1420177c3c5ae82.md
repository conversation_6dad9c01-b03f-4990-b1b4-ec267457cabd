# Page snapshot

```yaml
- banner:
  - navigation "Main menu":
    - button "Expand/Collapse the Application Bar":
      - img
    - img "Main menu Rancher logo"
    - link "Home page navigation menu":
      - /url: /home
      - img
      - text: Home
    - button "Cluster menu item local":
      - img "Local Cluster icon"
      - paragraph: local
    - text: Global Apps
    - link "Main menu multi cluster app menu item Cluster Management":
      - /url: /c/_/manager/provisioning.cattle.io.cluster
      - text:  Cluster Management
    - link "Main menu multi cluster app menu item Continuous Delivery":
      - /url: /c/_/fleet
      - text:  Continuous Delivery
    - link "Main menu multi cluster app menu item Virtualization Management":
      - /url: /c/_/harvesterManager/harvesterhci.io.management.cluster
      - text:  Virtualization Management
    - link "Main menu multi cluster app menu item SaasAdmin":
      - /url: /saasAdmin/c/_/insights
      - text:  SaasAdmin
    - text: Configuration
    - link "Main menu configuration app menu item Users & Authentication":
      - /url: /c/_/auth
      - text:  Users & Authentication
    - link "Main menu configuration app menu item Global Settings":
      - /url: /c/_/settings
      - text:  Global Settings
    - link "About page link":
      - /url: /about
      - text: v2.11.3
  - img "Logo"
  - button "%qindex.action%":
    - img "%qindex.tooltip%"
  - button "Page actions menu": 
  - button "Open notification center": 
  - button "Open user menu":
    - img "User avatar image"
- navigation "Secondary menu":
  - list:
    - listitem:
      - link "Auditlog":
        - /url: /saasAdmin/c/_/Auditlog
    - listitem:
      - link "Config":
        - /url: /saasAdmin/c/_/Config
    - listitem:
      - link "DataExport":
        - /url: /saasAdmin/c/_/DataExport
    - listitem:
      - link "JourneyDemo":
        - /url: /saasAdmin/c/_/JourneyDemo
    - listitem:
      - link "Knowledgebase":
        - /url: /saasAdmin/c/_/Knowledgebase
- main "default layout":
  - heading "PLG Journey System Demo" [level=1]
  - paragraph: Demonstration of the Product-Led Growth journey system with manual triggers and state monitoring.
  - heading "Current System State" [level=2]
  - heading "Journey System" [level=3]
  - text: "Status: Active Definitions Loaded: 0 Current Journey: None"
  - heading "User Context" [level=3]
  - text: "First Login: No Clusters: Credentials: PLG Mode: Disabled"
  - heading "Available Journeys" [level=2]
  - heading "Demo Actions" [level=2]
  - button "Simulate First Login"
  - button "Simulate Cluster Creation"
  - button "Reset Demo State"
  - button "Clear All Progress"
- complementary: Details 
- text: SaasAdmin
```