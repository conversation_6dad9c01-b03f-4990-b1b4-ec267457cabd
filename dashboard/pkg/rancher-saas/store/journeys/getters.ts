import type { 
  JourneyState, 
  JourneyDefinition, 
  JourneyProgress, 
  JourneyStep,
  JourneyCondition,
  JourneyContext 
} from '../../types/journey';

export default {
  // Current Journey Getters
  currentJourney: (state: JourneyState): JourneyDefinition | null => state.currentJourney,
  
  currentProgress: (state: JourneyState): JourneyProgress | null => state.currentProgress,
  
  currentStep: (state: JourneyState): JourneyStep | null => {
    if (!state.currentJourney || !state.currentProgress) return null;

    const stepId = state.currentProgress.currentStepId;
    return state.currentJourney.steps.find(step => step.id === stepId) || null;
  },

  // Journey Queries
  getJourneyById: (state: JourneyState) => (id: string): JourneyDefinition | null => {
    return state.definitions[id] || null;
  },

  getProgressById: (state: JourneyState) => (journeyId: string): JourneyProgress | null => {
    return state.userProgress[journeyId] || null;
  },

  getAllJourneys: (state: JourneyState): JourneyDefinition[] => {
    return Object.values(state.definitions);
  },

  getAvailableJourneys: (state: JourneyState, getters: any, rootState: any, rootGetters: any) => (context?: any): JourneyDefinition[] => {
    // Check cache first
    if (state.cache.availableJourneys && !getters.isCacheExpired) {
      return state.cache.availableJourneys;
    }

    const journeys = Object.values(state.definitions).filter(journey => {
      // Check if journey should be available based on triggers and conditions
      return getters.shouldJourneyBeAvailable(journey, context);
    });

    return journeys;
  },

  getJourneysByCategory: (state: JourneyState) => (category: string): JourneyDefinition[] => {
    return Object.values(state.definitions).filter(journey => journey.category === category);
  },

  // Step Queries
  getCurrentStepIndex: (state: JourneyState): number => {
    if (!state.currentProgress) return 0;
    return state.currentProgress.currentStepIndex || 0;
  },

  getTotalSteps: (state: JourneyState): number => {
    if (!state.currentJourney) return 0;
    return state.currentJourney.steps.filter(step => !step.hidden).length;
  },

  getVisibleSteps: (state: JourneyState, getters: any): JourneyStep[] => {
    if (!state.currentJourney) return [];
    
    return state.currentJourney.steps.filter(step => {
      if (step.hidden) return false;
      if (!step.condition) return true;
      return getters.evaluateCondition(step.condition);
    });
  },

  getNextStep: (state: JourneyState, getters: any): JourneyStep | null => {
    const visibleSteps = getters.getVisibleSteps;
    const currentStep = getters.currentStep;

    if (!currentStep) return null;

    // Find current step in visible steps array
    const currentVisibleIndex = visibleSteps.findIndex(step => step.id === currentStep.id);
    if (currentVisibleIndex === -1) return null;

    // Return next visible step
    if (currentVisibleIndex >= visibleSteps.length - 1) return null;
    return visibleSteps[currentVisibleIndex + 1] || null;
  },

  getPreviousStep: (state: JourneyState, getters: any): JourneyStep | null => {
    const visibleSteps = getters.getVisibleSteps;
    const currentStep = getters.currentStep;

    if (!currentStep) return null;

    // Find current step in visible steps array
    const currentVisibleIndex = visibleSteps.findIndex(step => step.id === currentStep.id);
    if (currentVisibleIndex === -1) return null;

    // Return previous visible step
    if (currentVisibleIndex <= 0) return null;
    return visibleSteps[currentVisibleIndex - 1] || null;
  },

  // Navigation Capabilities
  canGoNext: (state: JourneyState, getters: any): boolean => {
    const currentStep = getters.currentStep;
    if (!currentStep) return false;

    // Check if current step is ready
    if (currentStep.ready === false) return false;

    // Always allow going next - either to next step or to complete journey
    return true;
  },

  canGoPrevious: (state: JourneyState, getters: any): boolean => {
    const currentIndex = getters.getCurrentStepIndex;
    return currentIndex > 0;
  },

  // Progress Queries
  getCompletionPercentage: (state: JourneyState, getters: any) => (journeyId?: string): number => {
    const progress = journeyId ? state.userProgress[journeyId] : state.currentProgress;
    const journey = journeyId ? state.definitions[journeyId] : state.currentJourney;

    if (!progress || !journey) return 0;

    const totalSteps = journey.steps.filter(step => !step.hidden).length;
    const completedSteps = progress.completedSteps.length;

    return totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  },

  isJourneyCompleted: (state: JourneyState) => (journeyId: string): boolean => {
    const progress = state.userProgress[journeyId];
    return progress?.status === 'completed';
  },

  isJourneyInProgress: (state: JourneyState) => (journeyId: string): boolean => {
    const progress = state.userProgress[journeyId];
    return progress?.status === 'in-progress';
  },

  isJourneySkipped: (state: JourneyState) => (journeyId: string): boolean => {
    const progress = state.userProgress[journeyId];
    return progress?.status === 'skipped';
  },

  // System State Queries
  getSystemState: (state: JourneyState): Record<string, any> => state.systemState,

  shouldTriggerJourney: (state: JourneyState, getters: any) => (event: string, context?: any): JourneyDefinition | null => {
    const availableJourneys = getters.getAvailableJourneys(context);
    
    for (const journey of availableJourneys) {
      for (const trigger of journey.triggers) {
        if (trigger.event === event && getters.evaluateTriggerConditions(trigger, context)) {
          return journey;
        }
      }
    }
    
    return null;
  },

  shouldJourneyBeAvailable: (state: JourneyState, getters: any) => (journey: JourneyDefinition, context?: any): boolean => {
    // Check if journey is already completed
    const progress = state.userProgress[journey.id];
    if (progress?.status === 'completed') return false;
    
    // Check trigger conditions
    for (const trigger of journey.triggers) {
      if (getters.evaluateTriggerConditions(trigger, context)) {
        return true;
      }
    }
    
    return false;
  },

  // Condition Evaluation
  evaluateCondition: (state: JourneyState, getters: any, rootState: any, rootGetters: any) => (condition: JourneyCondition): boolean => {
    if (!condition) return true;

    switch (condition.type) {
      case 'system-state':
        return getters.evaluateSystemStateCondition(condition);
      case 'user-property':
        return getters.evaluateUserPropertyCondition(condition, rootGetters['auth/user']);
      case 'route':
        return getters.evaluateRouteCondition(condition, rootState.route?.current);
      case 'custom':
        if (condition.customCheck) {
          const context: JourneyContext = {
            journey: state.currentJourney,
            progress: state.currentProgress,
            user: rootGetters['auth/user'],
            route: rootState.route?.current,
            store: rootState,
            systemState: state.systemState,
          };
          return condition.customCheck(context);
        }
        return true;
      default:
        return true;
    }
  },

  evaluateSystemStateCondition: (state: JourneyState) => (condition: JourneyCondition): boolean => {
    const value = state.systemState[condition.property];

    switch (condition.operator) {
      case 'equals':
        return value === condition.value;
      case 'not-equals':
        return value !== condition.value;
      case 'exists':
        return value !== undefined && value !== null;
      case 'not-exists':
        return value === undefined || value === null;
      case 'contains':
        return Array.isArray(value) ? value.includes(condition.value) : 
               typeof value === 'string' ? value.includes(condition.value) : false;
      case 'greater-than':
        return typeof value === 'number' && value > condition.value;
      case 'less-than':
        return typeof value === 'number' && value < condition.value;
      default:
        return true;
    }
  },

  evaluateUserPropertyCondition: (state: JourneyState) => (condition: JourneyCondition, user: any): boolean => {
    if (!user) return false;

    const value = user[condition.property];

    switch (condition.operator) {
      case 'equals':
        return value === condition.value;
      case 'not-equals':
        return value !== condition.value;
      case 'exists':
        return value !== undefined && value !== null;
      case 'not-exists':
        return value === undefined || value === null;
      default:
        return true;
    }
  },

  evaluateRouteCondition: (state: JourneyState) => (condition: JourneyCondition, route: any): boolean => {
    if (!route) return false;

    const value = route[condition.property] || route.query?.[condition.property] || route.params?.[condition.property];

    switch (condition.operator) {
      case 'equals':
        return value === condition.value;
      case 'not-equals':
        return value !== condition.value;
      case 'contains':
        return typeof value === 'string' && value.includes(condition.value);
      default:
        return true;
    }
  },

  evaluateTriggerConditions: (state: JourneyState, getters: any) => (trigger: any, context?: any): boolean => {
    if (!trigger.conditions || trigger.conditions.length === 0) return true;

    return trigger.conditions.every((condition: JourneyCondition) => 
      getters.evaluateCondition(condition)
    );
  },

  // Configuration and State
  isJourneysEnabled: (state: JourneyState): boolean => state.config.enableJourneys,
  
  isAnalyticsEnabled: (state: JourneyState): boolean => state.config.enableAnalytics,
  
  isDebugMode: (state: JourneyState): boolean => state.config.debugMode,
  
  isLoading: (state: JourneyState): boolean => state.isLoading,
  
  isActive: (state: JourneyState): boolean => state.isActive,

  // Cache Management
  isCacheExpired: (state: JourneyState): boolean => {
    if (!state.cache.lastCacheUpdate) return true;
    
    const now = new Date().getTime();
    const lastUpdate = new Date(state.cache.lastCacheUpdate).getTime();
    
    return (now - lastUpdate) > state.cache.cacheTimeout;
  },

  // Analytics Getters
  analytics: (state: JourneyState) => state.analytics,

  getJourneyAnalytics: (state: JourneyState) => (journeyId: string) => {
    return {
      startCount: state.analytics.journeyStartCount[journeyId] || 0,
      completionCount: state.analytics.journeyCompletionCount[journeyId] || 0,
      skipCount: state.analytics.journeySkipCount[journeyId] || 0,
      averageCompletionTime: state.analytics.averageCompletionTime[journeyId] || 0,
      completionRate: (() => {
        const starts = state.analytics.journeyStartCount[journeyId] || 0;
        const completions = state.analytics.journeyCompletionCount[journeyId] || 0;
        return starts > 0 ? (completions / starts) * 100 : 0;
      })(),
    };
  },

  getPopularChoices: (state: JourneyState) => (journeyId: string, stepId: string) => {
    const prefix = `${journeyId}:${stepId}:`;
    const choices: Record<string, number> = {};
    
    Object.keys(state.analytics.popularChoices).forEach(key => {
      if (key.startsWith(prefix)) {
        const choiceId = key.substring(prefix.length);
        choices[choiceId] = state.analytics.popularChoices[key];
      }
    });
    
    return choices;
  },

  getDropOffPoints: (state: JourneyState) => (journeyId: string) => {
    const prefix = `${journeyId}:`;
    const dropOffs: Record<string, number> = {};
    
    Object.keys(state.analytics.dropOffPoints).forEach(key => {
      if (key.startsWith(prefix)) {
        const stepId = key.substring(prefix.length);
        dropOffs[stepId] = state.analytics.dropOffPoints[key];
      }
    });
    
    return dropOffs;
  },

  // User Progress
  userProgress: (state: JourneyState): Record<string, JourneyProgress> => state.userProgress,

  // Error Handling
  getLastError: (state: JourneyState): Error | null => state.lastError,

  getErrors: (state: JourneyState): any[] => state.errors,

  hasErrors: (state: JourneyState): boolean => state.errors.length > 0,
};
