<template>
  <div class="journey-demo">
    <!-- <PERSON> Header -->
    <div class="header">
      <h1>🚀 PLG Journey System Demo</h1>
      <p class="header-subtitle">Interactive demonstration of the Product-Led Growth journey system for stakeholder review</p>
    </div>

    <!-- Demo Overview -->
    <div class="section overview-section">
      <h2>📖 Demo Overview</h2>
      <div class="overview-grid">
        <div class="overview-card">
          <h3>🎯 What You'll See</h3>
          <ul>
            <li><strong>Decision Trees:</strong> Interactive choice-driven workflows</li>
            <li><strong>Conditional Logic:</strong> Smart choices based on user state</li>
            <li><strong>Workflow Integration:</strong> Real Rancher action execution</li>
            <li><strong>Analytics Dashboard:</strong> User behavior insights</li>
          </ul>
        </div>
        <div class="overview-card">
          <h3>💼 Business Value</h3>
          <ul>
            <li><strong>Guided Onboarding:</strong> Reduce time-to-value for new users</li>
            <li><strong>Feature Discovery:</strong> Help users find relevant capabilities</li>
            <li><strong>Data-Driven Optimization:</strong> Track and improve user journeys</li>
            <li><strong>Personalized Experience:</strong> Adapt to user context and needs</li>
          </ul>
        </div>
        <div class="overview-card">
          <h3>🔧 Technical Features</h3>
          <ul>
            <li><strong>Vue 3 + Vuex:</strong> Modern reactive architecture</li>
            <li><strong>Rancher Integration:</strong> Native dashboard components</li>
            <li><strong>State Persistence:</strong> Progress saved across sessions</li>
            <li><strong>Extensible Design:</strong> Easy to add new journeys</li>
          </ul>
        </div>
      </div>
      <div class="demo-navigation">
        <h4>🧭 How to Navigate This Demo:</h4>
        <p>Each section below has <strong>clear instructions</strong> marked with 🎯. Follow the steps to see the journey system in action. Look for <strong>Expected Outcome</strong> descriptions to understand what should happen.</p>
      </div>
    </div>

    <!-- System State Display -->
    <div class="section">
      <h2>Current System State</h2>
      <div class="state-grid">
        <div class="state-card">
          <h3>Journey System</h3>
          <div class="state-item">
            <span class="label">Status:</span>
            <span :class="['value', isLoading ? 'loading' : 'active']">
              {{ isLoading ? 'Loading...' : 'Active' }}
            </span>
          </div>
          <div class="state-item">
            <span class="label">Definitions Loaded:</span>
            <span class="value">{{ Object.keys(definitions).length }}</span>
          </div>
          <div class="state-item">
            <span class="label">Current Journey:</span>
            <span class="value">{{ currentJourney?.name || 'None' }}</span>
          </div>
        </div>

        <div class="state-card">
          <h3>User Context</h3>
          <div class="state-item">
            <span class="label">First Login:</span>
            <span class="value">{{ systemState.isFirstLogin ? 'Yes' : 'No' }}</span>
          </div>
          <div class="state-item">
            <span class="label">Clusters:</span>
            <span class="value">{{ systemState.clusterCount }}</span>
          </div>
          <div class="state-item">
            <span class="label">Credentials:</span>
            <span class="value">{{ systemState.credentialCount }}</span>
          </div>
          <div class="state-item">
            <span class="label">PLG Mode:</span>
            <span class="value">{{ systemState.isPLGMode ? 'Enabled' : 'Disabled' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Journeys -->
    <div class="section">
      <h2>Available Journeys</h2>
      <div class="journey-grid">
        <div 
          v-for="journey in availableJourneys" 
          :key="journey.id" 
          class="journey-card"
        >
          <div class="journey-header">
            <h3>{{ journey.name }}</h3>
            <span class="journey-category">{{ journey.category }}</span>
          </div>
          <p class="journey-description">{{ journey.description }}</p>
          
          <div class="journey-meta">
            <span class="duration">{{ journey.estimatedDuration || 'N/A' }} min</span>
            <span class="difficulty">{{ journey.difficulty || 'beginner' }}</span>
            <span class="steps">{{ journey.steps.length }} steps</span>
          </div>

          <div class="journey-actions">
            <button 
              class="btn btn-sm role-primary"
              :disabled="isLoading || isJourneyInProgress(journey.id)"
              @click="startJourney(journey.id)"
            >
              {{ isJourneyInProgress(journey.id) ? 'In Progress' : 'Start Journey' }}
            </button>
            
            <button 
              v-if="isJourneyInProgress(journey.id)"
              class="btn btn-sm role-secondary"
              @click="resumeJourney(journey.id)"
            >
              Resume
            </button>
          </div>

          <!-- Progress indicator if journey is in progress -->
          <div v-if="isJourneyInProgress(journey.id)" class="progress-indicator">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: getCompletionPercentage(journey.id) + '%' }"
              ></div>
            </div>
            <span class="progress-text">{{ Math.round(getCompletionPercentage(journey.id)) }}% complete</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Demo Actions -->
    <div class="section">
      <h2>🎮 Demo Actions</h2>
      <p class="section-description">
        <strong>What this demonstrates:</strong> How the journey system responds to different user events and system state changes.
      </p>

      <div class="demo-instructions">
        <div class="instruction-box">
          <h4>🎯 Try These Actions:</h4>
          <ul>
            <li><strong>"Simulate First Login"</strong> - Triggers onboarding journeys for new users</li>
            <li><strong>"Simulate Cluster Creation"</strong> - Shows how system events can trigger follow-up journeys</li>
            <li><strong>"Reset Demo State"</strong> - Resets the system to initial demo conditions</li>
            <li><strong>"Clear All Progress"</strong> - Removes all journey progress and analytics data</li>
          </ul>
          <p class="expected-outcome"><strong>Expected Outcome:</strong>
            <br>• <strong>"Simulate First Login":</strong> Watch "Current Journey" change to "Getting Started with Rancher" and a journey wizard will appear
            <br>• <strong>"Simulate Cluster Creation":</strong> Watch "Current Journey" change to "Configure Your New Cluster" and a new journey section will appear below
            <br>• Both actions demonstrate how different events automatically trigger appropriate follow-up journeys
          </p>
        </div>
      </div>

      <div class="demo-actions">
        <button
          class="btn role-secondary"
          :disabled="isLoading"
          @click="simulateFirstLogin"
        >
          🚀 Simulate First Login
        </button>

        <button
          class="btn role-secondary"
          :disabled="isLoading"
          @click="simulateClusterCreation"
        >
          ⚡ Simulate Cluster Creation
        </button>

        <button
          class="btn role-tertiary"
          :disabled="isLoading"
          @click="resetDemoState"
        >
          🔄 Reset Demo State
        </button>

        <button
          class="btn role-tertiary"
          :disabled="isLoading"
          @click="clearAllProgress"
        >
          🗑️ Clear All Progress
        </button>
      </div>
    </div>

    <!-- Current Journey Display -->
    <div v-if="currentJourney" class="section">
      <h2>Current Journey: {{ currentJourney.name }}</h2>
      <div class="current-journey">
        <ProgressTracker
          :current-step="currentStepIndex + 1"
          :total-steps="currentJourney.steps.length"
          :journey="currentJourney"
          :progress="currentProgress"
          :show-step-indicators="true"
          :show-restart-button="true"
          @restart-journey="restartCurrentJourney"
        />
        
        <div class="journey-controls">
          <button 
            class="btn role-secondary"
            :disabled="!canGoPrevious"
            @click="previousStep"
          >
            Previous
          </button>
          
          <button 
            class="btn role-primary"
            :disabled="!canGoNext"
            @click="nextStep"
          >
            Next
          </button>
          
          <button 
            class="btn role-tertiary"
            @click="skipJourney"
          >
            Skip Journey
          </button>
        </div>
      </div>
    </div>

    <!-- Decision Tree Demo Section -->
    <div class="section">
      <h2>🌳 Decision Tree Demo</h2>
      <p class="section-description">
        <strong>What this demonstrates:</strong> Interactive decision trees that guide users through complex choices with branching logic and conditional availability.
      </p>

      <div class="demo-subsection">
        <h3>📋 Interactive Decision Step</h3>
        <div class="demo-instructions">
          <div class="instruction-box">
            <h4>🎯 What This Demonstrates:</h4>
            <p><strong>Interactive Decision Trees:</strong> This shows how the PLG Journey System captures user choices and dynamically routes them through personalized workflows based on their selections.</p>

            <h4>🎯 Try This:</h4>
            <ol>
              <li><strong>Click on any cloud strategy square below</strong> - Each square represents a different user path through the journey</li>
              <li><strong>Watch the immediate visual feedback</strong> - The selected square will show a checkmark and highlight</li>
              <li><strong>See the success banner appear</strong> - This shows how the system captures and processes the decision</li>
              <li><strong>Notice the "Next Step" information</strong> - This demonstrates how each choice leads to different workflow branches</li>
            </ol>

            <h4>🎯 Expected Outcome:</h4>
            <p><strong>When you click a square:</strong>
              <br>• ✅ The square will show a green checkmark and become highlighted
              <br>• 🎉 A green success banner will appear below the choices
              <br>• 📋 The banner will show: "Choice Selected: [Your Choice]" and "Next Step: [workflow-id]"
              <br>• 🔄 This simulates how real users would be routed to different parts of the Rancher interface
              <br>• 📊 In production, this choice data would be captured for analytics and journey optimization
            </p>

            <h4>💼 Business Value:</h4>
            <p>This demonstrates how the system can <strong>personalize user experiences</strong> by capturing preferences and routing users through the most relevant workflows for their specific use case.</p>
          </div>
        </div>

        <DecisionTree
          :step="sampleDecisionStep"
          :choices="sampleDecisionChoices"
          :selected-choice="selectedDemoChoice"
          :allow-help="true"
          :allow-skip="true"
          @choice-selected="handleDemoChoiceSelected"
          @choice-skipped="handleDemoChoiceSkipped"
        />

        <div v-if="selectedDemoChoice" class="choice-result">
          <Banner color="success" icon="icon-checkmark">
            <template #default>
              <strong>✅ Choice Selected:</strong> {{ getSelectedChoiceLabel() }}
              <br>
              <strong>➡️ Next Step:</strong> {{ getSelectedChoiceNext() }}
              <br>
              <em>This would normally navigate the user to the appropriate workflow.</em>
            </template>
          </Banner>
        </div>
      </div>

      <div class="demo-subsection">
        <h3>🔒 Conditional Choices Demo</h3>
        <div class="demo-instructions">
          <div class="instruction-box">
            <h4>🎯 What This Demonstrates:</h4>
            <p><strong>Smart Conditional Logic:</strong> This shows how the PLG Journey System intelligently shows or hides options based on the user's current state and capabilities, preventing frustration and guiding users toward successful outcomes.</p>

            <h4>🎯 Try This:</h4>
            <ol>
              <li><strong>Check/uncheck the checkboxes below</strong> - These simulate different user states (credentials, clusters, experience level)</li>
              <li><strong>Watch the choice squares change in real-time</strong> - Available choices show checkmarks, unavailable ones show lock icons</li>
              <li><strong>Try checking "Has Cloud Credentials"</strong> - Watch "Create New Cluster" become clickable</li>
              <li><strong>Try checking "Has Clusters"</strong> - Watch "Manage Existing Clusters" become available</li>
              <li><strong>Try checking "Advanced User"</strong> - Watch "Advanced Configuration" unlock</li>
            </ol>

            <h4>🎯 Expected Outcome:</h4>
            <p><strong>When you check/uncheck boxes:</strong>
              <br>• 🔓 Locked choices (🔒) will become available (✅) when conditions are met
              <br>• 🔒 Available choices will become locked when conditions are removed
              <br>• 🎯 "Basic Setup" is always available (no conditions required)
              <br>• 🖱️ Available choices become clickable and show hover effects
              <br>• 🚫 Locked choices are grayed out and non-interactive
            </p>

            <h4>💼 Business Value:</h4>
            <p>This prevents users from hitting dead ends or error states by <strong>only showing options they can actually complete</strong>, leading to higher success rates and better user experience.</p>
          </div>
        </div>

        <div class="conditional-demo">
          <div class="condition-controls">
            <h4>🎛️ Simulate User State:</h4>
            <label class="checkbox-container">
              <input
                v-model="demoConditions.hasCredentials"
                type="checkbox"
                @change="updateDemoConditions"
              >
              <span class="checkmark"></span>
              Has Cloud Credentials
            </label>
            <label class="checkbox-container">
              <input
                v-model="demoConditions.hasClusters"
                type="checkbox"
                @change="updateDemoConditions"
              >
              <span class="checkmark"></span>
              Has Clusters
            </label>
            <label class="checkbox-container">
              <input
                v-model="demoConditions.isAdvancedUser"
                type="checkbox"
                @change="updateDemoConditions"
              >
              <span class="checkmark"></span>
              Advanced User
            </label>
          </div>

          <DecisionTree
            :step="conditionalDecisionStep"
            :choices="conditionalDecisionChoices"
            :selected-choice="selectedConditionalChoice"
            :allow-help="true"
            @choice-selected="handleConditionalChoiceSelected"
          />
        </div>
      </div>
    </div>

    <!-- Workflow Integration Demo Section -->
    <div class="section">
      <h2>⚙️ Workflow Integration Demo</h2>
      <p class="section-description">
        <strong>What this demonstrates:</strong> How journeys integrate with actual Rancher functionality to execute real actions like creating clusters, credentials, and resources.
      </p>

      <div class="demo-subsection">
        <h3>🔄 Workflow State Simulation</h3>
        <div class="demo-instructions">
          <div class="instruction-box">
            <h4>🎯 What This Demonstrates:</h4>
            <p><strong>Workflow State Management:</strong> This shows how the PLG Journey System manages different phases of workflow execution, providing visual feedback and state tracking for complex operations.</p>

            <h4>🎯 Try This:</h4>
            <ol>
              <li><strong>Click the state buttons below</strong> to simulate different workflow phases</li>
              <li><strong>Watch the button highlighting</strong> - The active state button will be highlighted</li>
              <li><strong>Notice the workflow component</strong> - Shows the basic workflow structure</li>
              <li><strong>Observe state persistence</strong> - The system remembers which state is active</li>
            </ol>

            <h4>🎯 Expected Outcome:</h4>
            <p><strong>When you click state buttons:</strong>
              <br>• 🎯 The clicked button will become highlighted (active state)
              <br>• 📋 The workflow component shows "Create Cloud Credential" workflow
              <br>• 🔄 This demonstrates state management for workflow phases
              <br>• 💼 In production, each state would show different UI components and actions
            </p>

            <h4>💼 Business Value:</h4>
            <p>This demonstrates how the system can <strong>guide users through complex multi-step processes</strong> with clear state management and visual feedback, reducing errors and improving completion rates.</p>
          </div>
        </div>

        <div class="workflow-state-controls">
          <h4>🎛️ Simulate Workflow State:</h4>
          <button
            v-for="state in workflowStates"
            :key="state.value"
            class="btn btn-sm"
            :class="demoWorkflowState === state.value ? 'role-primary' : 'role-secondary'"
            @click="setDemoWorkflowState(state.value)"
          >
            {{ state.label }}
          </button>
        </div>

        <WorkflowIntegration
          :step="sampleWorkflowStep"
          :workflow-state="demoWorkflowState"
          :progress="demoWorkflowProgress"
          :execution-logs="demoExecutionLogs"
          :context="demoWorkflowContext"
          :allow-skip="true"
          @workflow-executed="handleDemoWorkflowExecuted"
          @workflow-skipped="handleDemoWorkflowSkipped"
          @demo-auto-progress="handleDemoAutoProgress"
        />
      </div>
    </div>

    <!-- Journey Analytics Dashboard -->
    <div class="section">
      <h2>📊 Journey Analytics Dashboard</h2>
      <p class="section-description">
        <strong>What this demonstrates:</strong> Real-time analytics and metrics that help product teams understand user behavior, optimize journeys, and measure PLG success.
      </p>

      <div class="demo-instructions">
        <div class="instruction-box">
          <h4>🎯 What You're Seeing:</h4>
          <ul>
            <li><strong>Completion Rates:</strong> How many users finish each journey (higher is better)</li>
            <li><strong>Popular Choices:</strong> Which decision paths users prefer most</li>
            <li><strong>User Paths:</strong> The actual step-by-step routes users take</li>
            <li><strong>System Metrics:</strong> Overall journey system performance</li>
          </ul>
          <p class="expected-outcome"><strong>Business Value:</strong> This data helps identify where users drop off, which features are most popular, and how to optimize the user experience.</p>
        </div>
      </div>

      <div class="analytics-grid">
        <div class="analytics-card">
          <h3>📈 Journey Completion Rates</h3>
          <div class="completion-stats">
            <div
              v-for="(rate, journeyId) in completionRates"
              :key="journeyId"
              class="completion-item"
            >
              <span class="journey-name">{{ getJourneyName(journeyId) }}</span>
              <div class="completion-bar">
                <div
                  class="completion-fill"
                  :style="{ width: rate + '%' }"
                ></div>
              </div>
              <span class="completion-percentage">{{ rate }}%</span>
            </div>
            <div v-if="Object.keys(completionRates).length === 0" class="no-data">
              <em>📝 Data will appear here as users complete journeys</em>
            </div>
          </div>
        </div>

        <div class="analytics-card">
          <h3>🏆 Popular Choices</h3>
          <div class="popular-choices">
            <div
              v-for="(count, choice) in popularChoices"
              :key="choice"
              class="choice-stat"
            >
              <span class="choice-name">{{ choice }}</span>
              <span class="choice-count">{{ count }} selections</span>
            </div>
            <div v-if="Object.keys(popularChoices).length === 0" class="no-data">
              <em>📝 Make some choices above to see popularity data</em>
            </div>
          </div>
        </div>

        <div class="analytics-card">
          <h3>🛤️ User Journey Paths</h3>
          <div class="user-paths">
            <div
              v-for="(path, index) in recentUserPaths"
              :key="index"
              class="path-item"
            >
              <div class="path-steps">
                <span
                  v-for="(step, stepIndex) in path.steps"
                  :key="stepIndex"
                  class="path-step"
                >
                  {{ step }}
                  <i v-if="stepIndex < path.steps.length - 1" class="icon icon-chevron-right"></i>
                </span>
              </div>
              <span class="path-timestamp">{{ formatTimestamp(path.timestamp) }}</span>
            </div>
            <div v-if="recentUserPaths.length === 0" class="no-data">
              <em>📝 User journey paths will appear here as journeys are completed</em>
            </div>
          </div>
        </div>

        <div class="analytics-card">
          <h3>📋 System Metrics</h3>
          <div class="system-metrics">
            <div class="metric-item">
              <span class="metric-label">Total Journeys Started:</span>
              <span class="metric-value">{{ totalJourneysStarted }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">Total Journeys Completed:</span>
              <span class="metric-value">{{ totalJourneysCompleted }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">Average Completion Time:</span>
              <span class="metric-value">{{ averageCompletionTime }} min</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">Active Journeys:</span>
              <span class="metric-value">{{ activeJourneysCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Journey Wizard Modal -->
    <JourneyWizard
      v-if="showJourneyWizard && currentJourney"
      :journey-id="currentJourney.id"
      @journey-completed="handleJourneyCompleted"
      @journey-skipped="handleJourneySkipped"
      @journey-error="handleJourneyError"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { mapGetters, mapActions } from 'vuex';
import ProgressTracker from '../components/journey/ProgressTracker.vue';
import JourneyWizard from '../components/journey/JourneyWizard.vue';
import DecisionTree from '../components/journey/DecisionTree.vue';
import WorkflowIntegration from '../components/journey/WorkflowIntegration.vue';
import { Banner } from '@components/Banner';

export default defineComponent({
  name: 'JourneyDemo',

  components: {
    ProgressTracker,
    JourneyWizard,
    DecisionTree,
    WorkflowIntegration,
    Banner,
  },

  data() {
    return {
      showJourneyWizard: false,

      // Demo state for Decision Tree
      selectedDemoChoice: null,
      selectedConditionalChoice: null,
      demoConditions: {
        hasCredentials: false,
        hasClusters: false,
        isAdvancedUser: false,
      },

      // Demo state for Workflow Integration
      demoWorkflowState: 'ready' as 'ready' | 'executing' | 'completed' | 'error',
      demoWorkflowProgress: 0,
      demoExecutionLogs: [],
    };
  },

  async mounted() {
    try {
      // Initialize the journey system and load analytics data
      console.log('JourneyDemo: Initializing journey system...');

      // Initialize journey system if not already done
      await this.$store.dispatch('journeys/initializeJourneySystem', { demoMode: true });

      // Load analytics data from preferences
      const analytics = await this.$store.dispatch('journeys/loadJourneyAnalytics');
      this.$store.commit('journeys/SET_ANALYTICS', analytics);

      // Setup demo system state
      await this.$store.dispatch('journeys/setupDemoSystemState');

      console.log('JourneyDemo: Initialization complete', { analytics });
    } catch (error) {
      console.error('Failed to initialize JourneyDemo:', error);
      this.$store.dispatch('growl/error', {
        title: 'Demo Initialization Error',
        message: 'Failed to initialize the journey demo system'
      });
    }
  },

  computed: {
    // Journey store getters with fallbacks
    definitions() {
      return this.$store.getters['journeys/definitions'] || {};
    },
    currentJourney() {
      return this.$store.getters['journeys/currentJourney'] || null;
    },
    currentProgress() {
      return this.$store.getters['journeys/currentProgress'] || null;
    },
    systemState() {
      return this.$store.getters['journeys/systemState'] || {};
    },
    isLoading() {
      return this.$store.getters['journeys/isLoading'] || false;
    },
    availableJourneys() {
      return this.$store.getters['journeys/availableJourneys'] || [];
    },
    currentStepIndex() {
      return this.$store.getters['journeys/getCurrentStepIndex'] || 0;
    },
    canGoNext() {
      return this.$store.getters['journeys/canGoNext'] || false;
    },
    canGoPrevious() {
      return this.$store.getters['journeys/canGoPrevious'] || false;
    },
    getCompletionPercentage() {
      return this.$store.getters['journeys/getCompletionPercentage'] || (() => 0);
    },
    isJourneyInProgress() {
      return this.$store.getters['journeys/isJourneyInProgress'] || (() => false);
    },

    // Demo data computed properties
    sampleDecisionStep() {
      return {
        id: 'demo-decision',
        name: 'demo-decision',
        label: 'Choose Your Cloud Strategy',
        type: 'decision' as const,
        question: 'What type of cloud infrastructure do you want to set up?',
        description: 'Select the option that best matches your organization\'s needs and current infrastructure.',
        content: '<p>This decision will help us guide you through the most appropriate setup process for your use case.</p>',
      };
    },

    sampleDecisionChoices() {
      return [
        {
          id: 'multi-cloud',
          label: 'Multi-Cloud Setup',
          description: 'Deploy across multiple cloud providers for redundancy and flexibility',
          icon: 'icon-globe',
          next: 'multi-cloud-setup',
        },
        {
          id: 'single-cloud',
          label: 'Single Cloud Provider',
          description: 'Focus on one cloud provider for simplicity and cost optimization',
          icon: 'icon-cloud',
          next: 'single-cloud-setup',
        },
        {
          id: 'hybrid-cloud',
          label: 'Hybrid Cloud',
          description: 'Combine on-premises infrastructure with cloud resources',
          icon: 'icon-network',
          next: 'hybrid-setup',
        },
        {
          id: 'edge-computing',
          label: 'Edge Computing',
          description: 'Deploy at the edge for low-latency applications',
          icon: 'icon-speed',
          next: 'edge-setup',
        },
      ];
    },

    conditionalDecisionStep() {
      return {
        id: 'conditional-demo',
        name: 'conditional-demo',
        label: 'Conditional Choices Demo',
        type: 'decision' as const,
        question: 'What would you like to do next?',
        description: 'These choices are conditionally available based on your current system state.',
      };
    },

    conditionalDecisionChoices() {
      return [
        {
          id: 'create-cluster',
          label: 'Create New Cluster',
          description: 'Create a new Kubernetes cluster (requires credentials)',
          icon: 'icon-plus',
          condition: {
            type: 'custom',
            customCheck: () => this.demoConditions.hasCredentials,
          },
        },
        {
          id: 'manage-clusters',
          label: 'Manage Existing Clusters',
          description: 'Manage your existing clusters (requires clusters)',
          icon: 'icon-gear',
          condition: {
            type: 'custom',
            customCheck: () => this.demoConditions.hasClusters,
          },
        },
        {
          id: 'advanced-config',
          label: 'Advanced Configuration',
          description: 'Advanced cluster configuration options (for advanced users)',
          icon: 'icon-code',
          condition: {
            type: 'custom',
            customCheck: () => this.demoConditions.isAdvancedUser,
          },
        },
        {
          id: 'basic-setup',
          label: 'Basic Setup',
          description: 'Simple setup process (always available)',
          icon: 'icon-checkmark',
        },
      ];
    },

    sampleWorkflowStep() {
      return {
        id: 'demo-workflow',
        name: 'demo-workflow',
        label: 'Create Cloud Credential',
        type: 'workflow' as const,
        description: 'Set up your cloud provider credentials to enable cluster creation',
        action: {
          type: 'create-resource' as const,
          target: 'cloudCredential',
          params: {
            provider: 'aws',
            name: 'demo-aws-credential',
          },
        },
      };
    },

    workflowStates() {
      return [
        { value: 'ready' as const, label: 'Ready' },
        { value: 'executing' as const, label: 'Executing' },
        { value: 'completed' as const, label: 'Completed' },
        { value: 'error' as const, label: 'Error' },
      ];
    },

    // Analytics computed properties
    completionRates() {
      const analytics = this.$store.getters['journeys/analytics'] || {};
      const startCount = analytics.journeyStartCount || {};
      const completionCount = analytics.journeyCompletionCount || {};

      const rates = {};
      Object.keys(startCount).forEach(journeyId => {
        const started = startCount[journeyId] || 0;
        const completed = completionCount[journeyId] || 0;
        rates[journeyId] = started > 0 ? Math.round((completed / started) * 100) : 0;
      });

      return rates;
    },

    popularChoices() {
      const analytics = this.$store.getters['journeys/analytics'] || {};
      return analytics.popularChoices || {};
    },

    recentUserPaths() {
      const analytics = this.$store.getters['journeys/analytics'] || {};
      const paths = analytics.userPaths || [];
      return paths.slice(-5); // Show last 5 paths
    },

    totalJourneysStarted() {
      const analytics = this.$store.getters['journeys/analytics'] || {};
      console.log('totalJourneysStarted - analytics:', JSON.stringify(analytics, null, 2));
      const startCount = analytics.journeyStartCount || {};
      console.log('totalJourneysStarted - startCount:', JSON.stringify(startCount, null, 2));
      const total = Object.values(startCount).reduce((sum, count) => sum + count, 0);
      console.log('totalJourneysStarted - total:', total);
      return total;
    },

    totalJourneysCompleted() {
      const analytics = this.$store.getters['journeys/analytics'] || {};
      const completionCount = analytics.journeyCompletionCount || {};
      return Object.values(completionCount).reduce((sum, count) => sum + count, 0);
    },

    averageCompletionTime() {
      const analytics = this.$store.getters['journeys/analytics'] || {};
      const avgTimes = analytics.averageCompletionTime || {};
      const times = Object.values(avgTimes).filter(time => time > 0);
      if (times.length === 0) return 0;
      return Math.round(times.reduce((sum, time) => sum + time, 0) / times.length);
    },

    activeJourneysCount() {
      const userProgress = this.$store.getters['journeys/userProgress'] || {};
      return Object.keys(userProgress).filter(journeyId => {
        const progress = userProgress[journeyId];
        return progress && progress.status === 'in-progress';
      }).length;
    },

    demoWorkflowContext() {
      return {
        journey: this.currentJourney,
        progress: this.currentProgress,
        user: this.$store.getters['auth/user'],
        route: this.$route,
        store: this.$store,
      };
    },
  },

  methods: {
    // Journey store actions
    async triggerJourneyManually(payload) {
      return this.$store.dispatch('journeys/triggerJourneyManually', payload);
    },
    async simulateFirstLogin() {
      try {
        await this.$store.dispatch('journeys/simulateFirstLogin');

        // Show success message to highlight the journey transition
        this.$store.dispatch('growl/success', {
          title: '🚀 First Login Simulated!',
          message: 'First login event triggered the "Getting Started with Rancher" journey. Look for the journey wizard below!',
          timeout: 8000
        });
      } catch (error) {
        console.error('Failed to simulate first login:', error);
        this.$store.dispatch('growl/error', {
          title: 'Demo Error',
          message: 'Failed to simulate first login event'
        });
      }
    },
    async simulateClusterCreation(payload) {
      try {
        await this.$store.dispatch('journeys/simulateClusterCreation', payload);

        // Show success message to highlight the journey transition
        this.$store.dispatch('growl/success', {
          title: '🎉 System Event Triggered!',
          message: 'Cluster creation event triggered the "Configure Your New Cluster" journey. Look for the new journey section below!',
          timeout: 8000
        });
      } catch (error) {
        console.error('Failed to simulate cluster creation:', error);
        this.$store.dispatch('growl/error', {
          title: 'Demo Error',
          message: 'Failed to simulate cluster creation event'
        });
      }
    },
    async setupDemoSystemState() {
      return this.$store.dispatch('journeys/setupDemoSystemState');
    },
    async clearAllJourneyData() {
      return this.$store.dispatch('journeys/clearAllJourneyData');
    },
    async resumeJourney(journeyId) {
      return this.$store.dispatch('journeys/resumeJourney', journeyId);
    },
    async nextStep() {
      return this.$store.dispatch('journeys/nextStep');
    },
    async previousStep() {
      return this.$store.dispatch('journeys/previousStep');
    },
    async skipJourney() {
      if (this.currentJourney) {
        return this.$store.dispatch('journeys/skipJourney', this.currentJourney.id);
      }
    },
    async completeJourney() {
      return this.$store.dispatch('journeys/completeJourney');
    },

    async startJourney(journeyId: string) {
      try {
        await this.triggerJourneyManually({ journeyId });
        this.showJourneyWizard = true;
      } catch (error) {
        console.error('Failed to start journey:', error);
        this.$store.dispatch('growl/error', {
          title: 'Journey Error',
          message: `Failed to start journey: ${error.message}`,
        });
      }
    },

    async resetDemoState() {
      try {
        // Clear current journey first
        await this.$store.dispatch('journeys/clearCurrentJourney');

        // Reset demo system state
        await this.setupDemoSystemState();

        this.$store.dispatch('growl/success', {
          title: 'Demo Reset',
          message: 'Demo system state has been reset',
        });
      } catch (error) {
        console.error('Failed to reset demo state:', error);
      }
    },

    async clearAllProgress() {
      try {
        // Clear all journey data from preferences
        await this.clearAllJourneyData();

        // Clear current journey state from UI
        this.$store.commit('journeys/CLEAR_CURRENT_JOURNEY');

        // Clear all user progress from in-memory state
        this.$store.commit('journeys/CLEAR_ALL_USER_PROGRESS');

        this.showJourneyWizard = false;
        this.$store.dispatch('growl/success', {
          title: 'Progress Cleared',
          message: 'All journey progress has been cleared',
        });
      } catch (error) {
        console.error('Failed to clear progress:', error);
      }
    },

    async restartCurrentJourney() {
      if (this.currentJourney) {
        await this.startJourney(this.currentJourney.id);
      }
    },

    handleJourneyCompleted(event: any) {
      this.showJourneyWizard = false;
      this.$store.dispatch('growl/success', {
        title: 'Journey Completed',
        message: `Successfully completed: ${event.journey?.name}`,
      });
    },

    handleJourneySkipped(event: any) {
      this.showJourneyWizard = false;
      this.$store.dispatch('growl/info', {
        title: 'Journey Skipped',
        message: `Skipped: ${event.journey?.name}`,
      });
    },

    handleJourneyError(event: any) {
      this.showJourneyWizard = false;
      this.$store.dispatch('growl/error', {
        title: 'Journey Error',
        message: `Error in journey: ${event.error?.message}`,
      });
    },

    // Demo methods for Decision Tree
    handleDemoChoiceSelected(choice) {
      this.selectedDemoChoice = choice.id;
      console.log('Demo choice selected:', choice);

      // Simulate analytics tracking
      this.$store.dispatch('journeys/trackChoiceSelection', {
        journeyId: 'demo-journey',
        stepId: 'demo-decision',
        choiceId: choice.id,
        choiceLabel: choice.label,
      });
    },

    handleDemoChoiceSkipped() {
      this.selectedDemoChoice = null;
      console.log('Demo choice skipped');
    },

    handleConditionalChoiceSelected(choice) {
      this.selectedConditionalChoice = choice.id;
      console.log('Conditional choice selected:', choice);
    },

    updateDemoConditions() {
      // Force reactivity update
      this.$forceUpdate();
    },

    getSelectedChoiceLabel() {
      if (!this.selectedDemoChoice) return '';
      const choice = this.sampleDecisionChoices.find(c => c.id === this.selectedDemoChoice);
      return choice ? choice.label : '';
    },

    getSelectedChoiceNext() {
      if (!this.selectedDemoChoice) return '';
      const choice = this.sampleDecisionChoices.find(c => c.id === this.selectedDemoChoice);
      return choice ? choice.next : '';
    },

    // Demo methods for Workflow Integration
    setDemoWorkflowState(state) {
      this.demoWorkflowState = state;

      // Simulate different states with appropriate data
      switch (state) {
        case 'ready':
          this.demoWorkflowProgress = 0;
          this.demoExecutionLogs = [];
          break;
        case 'executing':
          this.demoWorkflowProgress = 45;
          this.demoExecutionLogs = [
            { level: 'info', message: 'Starting credential creation...', timestamp: new Date() },
            { level: 'info', message: 'Validating AWS credentials...', timestamp: new Date() },
            { level: 'info', message: 'Creating credential resource...', timestamp: new Date() },
          ];
          break;
        case 'completed':
          this.demoWorkflowProgress = 100;
          this.demoExecutionLogs = [
            { level: 'info', message: 'Starting credential creation...', timestamp: new Date() },
            { level: 'info', message: 'Validating AWS credentials...', timestamp: new Date() },
            { level: 'info', message: 'Creating credential resource...', timestamp: new Date() },
            { level: 'success', message: 'Credential created successfully!', timestamp: new Date() },
          ];
          break;
        case 'error':
          this.demoWorkflowProgress = 30;
          this.demoExecutionLogs = [
            { level: 'info', message: 'Starting credential creation...', timestamp: new Date() },
            { level: 'info', message: 'Validating AWS credentials...', timestamp: new Date() },
            { level: 'error', message: 'Invalid AWS access key provided', timestamp: new Date() },
          ];
          break;
      }
    },

    handleDemoWorkflowExecuted() {
      console.log('Demo workflow executed');
      this.setDemoWorkflowState('executing');

      // Simulate execution progress
      setTimeout(() => {
        this.setDemoWorkflowState('completed');
      }, 3000);
    },

    handleDemoWorkflowSkipped() {
      console.log('Demo workflow skipped');
      this.setDemoWorkflowState('ready');
    },

    handleDemoAutoProgress(targetState) {
      console.log('Demo auto-progress to:', targetState);
      this.setDemoWorkflowState(targetState);
    },

    // Utility methods
    getJourneyName(journeyId) {
      const journey = this.definitions[journeyId];
      return journey ? journey.name : journeyId;
    },

    formatTimestamp(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString();
    },
  },
});
</script>

<style lang="scss" scoped>
.journey-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    margin-bottom: 30px;
    text-align: center;

    h1 {
      color: var(--primary);
      margin-bottom: 10px;
      font-size: 2.5rem;
    }

    .header-subtitle {
      color: var(--muted);
      font-size: 18px;
      font-weight: 500;
    }
  }

  // Overview section styles
  .overview-section {
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--box-bg) 100%);
    border: 2px solid var(--primary);
    margin-bottom: 40px;

    h2 {
      color: var(--primary);
      font-size: 1.8rem;
    }
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
  }

  .overview-card {
    padding: 20px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h3 {
      color: var(--primary);
      margin-bottom: 15px;
      font-size: 1.2rem;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.4;

        strong {
          color: var(--primary-text);
        }
      }
    }
  }

  .demo-navigation {
    padding: 20px;
    background: var(--info-bg);
    border: 1px solid var(--info);
    border-radius: 6px;
    text-align: center;

    h4 {
      color: var(--info);
      margin-bottom: 10px;
    }

    p {
      margin: 0;
      color: var(--body-text);
      font-size: 16px;
    }
  }

  .section {
    margin-bottom: 40px;
    padding: 20px;
    background: var(--box-bg);
    border: 1px solid var(--border);
    border-radius: 8px;

    h2 {
      margin-bottom: 20px;
      color: var(--body-text);
      border-bottom: 2px solid var(--primary);
      padding-bottom: 10px;
    }
  }

  .state-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }

  .state-card {
    padding: 15px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: 6px;

    h3 {
      margin-bottom: 15px;
      color: var(--primary);
    }

    .state-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .label {
        font-weight: 600;
        color: var(--body-text);
      }

      .value {
        color: var(--muted);

        &.loading {
          color: var(--warning);
        }

        &.active {
          color: var(--success);
        }
      }
    }
  }

  .journey-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }

  .journey-card {
    padding: 20px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .journey-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      h3 {
        margin: 0;
        color: var(--primary);
      }

      .journey-category {
        background: var(--primary);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        text-transform: uppercase;
      }
    }

    .journey-description {
      color: var(--muted);
      margin-bottom: 15px;
      line-height: 1.4;
    }

    .journey-meta {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;

      span {
        background: var(--box-bg);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: var(--muted);
      }
    }

    .journey-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .progress-indicator {
      .progress-bar {
        width: 100%;
        height: 6px;
        background: var(--border);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 5px;

        .progress-fill {
          height: 100%;
          background: var(--primary);
          transition: width 0.3s ease;
        }
      }

      .progress-text {
        font-size: 12px;
        color: var(--muted);
      }
    }
  }

  .demo-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  .current-journey {
    .journey-controls {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      justify-content: center;
    }
  }

  // Demo section styles
  .section-description {
    color: var(--body-text);
    margin-bottom: 20px;
    font-size: 16px;
    padding: 15px;
    background: var(--box-bg);
    border-left: 4px solid var(--primary);
    border-radius: 4px;

    strong {
      color: var(--primary);
    }
  }

  .demo-instructions {
    margin-bottom: 25px;
  }

  .instruction-box {
    padding: 20px;
    background: var(--warning-bg);
    border: 2px solid var(--warning);
    border-radius: 8px;
    margin-bottom: 20px;

    h4 {
      color: var(--warning);
      margin-bottom: 15px;
      font-size: 1.1rem;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    ol, ul {
      margin: 0 0 15px 0;
      padding-left: 25px;

      li {
        margin-bottom: 8px;
        line-height: 1.5;

        strong {
          color: var(--primary-text);
          font-weight: 600;
        }
      }
    }

    .expected-outcome {
      margin: 15px 0 0 0;
      padding: 12px;
      background: var(--success-bg);
      border: 1px solid var(--success);
      border-radius: 4px;
      font-size: 14px;

      strong {
        color: var(--success);
      }
    }
  }

  .demo-subsection {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: 6px;

    h3 {
      margin-bottom: 15px;
      color: var(--primary);
      border-bottom: 1px solid var(--border);
      padding-bottom: 8px;
      font-size: 1.3rem;
    }
  }

  .no-data {
    padding: 20px;
    text-align: center;
    color: var(--muted);
    font-style: italic;
  }

  .choice-result {
    margin-top: 20px;
  }

  .conditional-demo {
    .condition-controls {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      padding: 15px;
      background: var(--box-bg);
      border-radius: 4px;
      border: 1px solid var(--border);

      .checkbox-container {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        color: var(--body-text);

        input[type="checkbox"] {
          margin-right: 8px;
        }
      }
    }
  }

  .workflow-state-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--box-bg);
    border-radius: 4px;
    border: 1px solid var(--border);
  }

  // Analytics styles
  .analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }

  .analytics-card {
    padding: 20px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: 8px;

    h3 {
      margin-bottom: 15px;
      color: var(--primary);
      border-bottom: 2px solid var(--primary);
      padding-bottom: 8px;
    }
  }

  .completion-stats {
    .completion-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .journey-name {
        flex: 1;
        font-size: 14px;
        color: var(--body-text);
      }

      .completion-bar {
        flex: 2;
        height: 8px;
        background: var(--border);
        border-radius: 4px;
        overflow: hidden;

        .completion-fill {
          height: 100%;
          background: var(--success);
          transition: width 0.3s ease;
        }
      }

      .completion-percentage {
        flex: 0 0 40px;
        text-align: right;
        font-size: 12px;
        color: var(--muted);
      }
    }
  }

  .popular-choices {
    .choice-stat {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--border);

      &:last-child {
        border-bottom: none;
      }

      .choice-name {
        font-weight: 500;
        color: var(--body-text);
      }

      .choice-count {
        color: var(--muted);
        font-size: 12px;
      }
    }
  }

  .user-paths {
    .path-item {
      margin-bottom: 15px;
      padding: 10px;
      background: var(--box-bg);
      border-radius: 4px;
      border: 1px solid var(--border);

      .path-steps {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-bottom: 5px;
        flex-wrap: wrap;

        .path-step {
          font-size: 12px;
          color: var(--body-text);
          background: var(--primary);
          color: white;
          padding: 2px 6px;
          border-radius: 3px;
        }

        .icon {
          color: var(--muted);
          font-size: 10px;
        }
      }

      .path-timestamp {
        font-size: 11px;
        color: var(--muted);
      }
    }
  }

  .system-metrics {
    .metric-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--border);

      &:last-child {
        border-bottom: none;
      }

      .metric-label {
        color: var(--body-text);
        font-size: 14px;
      }

      .metric-value {
        color: var(--primary);
        font-weight: 600;
        font-size: 16px;
      }
    }
  }
}
</style>
