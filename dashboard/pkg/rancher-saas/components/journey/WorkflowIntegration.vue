<template>
  <div class="workflow-integration">
    <div class="workflow-header">
      <h3 v-if="step.label" class="workflow-title">
        {{ step.label }}
      </h3>
      <p v-if="step.description" class="workflow-description">
        {{ step.description }}
      </p>
    </div>

    <!-- Workflow Status -->
    <div class="workflow-status">
      <div class="status-indicator" :class="statusClass">
        <i :class="statusIcon" class="icon icon-lg"></i>
        <span class="status-text">{{ statusText }}</span>
      </div>
      
      <div v-if="isLoading" class="status-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
        <span class="progress-text">{{ progressText }}</span>
      </div>
    </div>

    <!-- Workflow Content -->
    <div class="workflow-content">
      <!-- Pre-execution state -->
      <div v-if="workflowState === 'ready'" class="workflow-ready">
        <div class="action-preview">
          <h4>{{ t('journey.workflow.aboutToExecute') }}</h4>
          <div class="action-details">
            <div class="action-type">
              <strong>{{ t('journey.workflow.actionType') }}:</strong>
              {{ getActionTypeLabel() }}
            </div>
            <div v-if="action?.target" class="action-target">
              <strong>{{ t('journey.workflow.target') }}:</strong>
              {{ action.target }}
            </div>
            <div v-if="actionParams" class="action-params">
              <strong>{{ t('journey.workflow.parameters') }}:</strong>
              <pre>{{ JSON.stringify(actionParams, null, 2) }}</pre>
            </div>
          </div>
        </div>

        <div class="workflow-actions">
          <AsyncButton
            mode="execute"
            class="btn role-primary"
            :disabled="!canExecute"
            @click="executeWorkflow"
          >
            {{ getExecuteButtonLabel() }}
          </AsyncButton>
          
          <button
            v-if="allowSkip"
            class="btn role-secondary ml-10"
            @click="skipWorkflow"
          >
            {{ t('journey.workflow.skip') }}
          </button>
        </div>
      </div>

      <!-- Execution in progress -->
      <div v-else-if="workflowState === 'executing'" class="workflow-executing">
        <div class="execution-status">
          <!-- In demo mode, use non-blocking spinner -->
          <Loading v-if="!isDemoMode" mode="relative" />
          <div v-else class="demo-spinner">
            <i class="icon icon-spinner icon-spin icon-lg"></i>
          </div>
          <h4>{{ t('journey.workflow.executing') }}</h4>
          <p>{{ currentExecutionStep }}</p>
        </div>

        <div v-if="executionLogs.length" class="execution-logs">
          <h5>{{ t('journey.workflow.executionLog') }}</h5>
          <div class="log-container">
            <div
              v-for="(log, index) in executionLogs"
              :key="index"
              class="log-entry"
              :class="log.level"
            >
              <span class="log-timestamp">{{ formatTimestamp(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Execution completed -->
      <div v-else-if="workflowState === 'completed'" class="workflow-completed">
        <div class="completion-status">
          <i class="icon icon-checkmark icon-2x text-success"></i>
          <h4>{{ t('journey.workflow.completed') }}</h4>
          <p>{{ completionMessage }}</p>
        </div>

        <div v-if="executionResult" class="execution-result">
          <h5>{{ t('journey.workflow.result') }}</h5>
          <div class="result-content">
            <pre v-if="typeof executionResult === 'object'">{{ JSON.stringify(executionResult, null, 2) }}</pre>
            <span v-else>{{ executionResult }}</span>
          </div>
        </div>

        <div v-if="nextActions.length" class="next-actions">
          <h5>{{ t('journey.workflow.nextActions') }}</h5>
          <div class="action-buttons">
            <button
              v-for="nextAction in nextActions"
              :key="nextAction.id"
              class="btn role-secondary"
              @click="executeNextAction(nextAction)"
            >
              {{ nextAction.label }}
            </button>
          </div>
        </div>
      </div>

      <!-- Execution failed -->
      <div v-else-if="workflowState === 'error'" class="workflow-error">
        <div class="error-status">
          <i class="icon icon-warning icon-2x text-error"></i>
          <h4>{{ t('journey.workflow.failed') }}</h4>
          <p>{{ errorMessage }}</p>
        </div>

        <div v-if="errorDetails" class="error-details">
          <Banner color="error" :closable="false">
            <template #default>
              <div class="error-content">
                <h5>{{ t('journey.workflow.errorDetails') }}</h5>
                <pre>{{ errorDetails }}</pre>
              </div>
            </template>
          </Banner>
        </div>

        <div class="error-actions">
          <button
            class="btn role-primary"
            @click="retryWorkflow"
          >
            {{ t('journey.workflow.retry') }}
          </button>
          
          <button
            class="btn role-secondary ml-10"
            @click="skipWorkflow"
          >
            {{ t('journey.workflow.skipAfterError') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Additional Content -->
    <div v-if="step.content" class="workflow-additional-content">
      <div v-html="step.content"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import { mapGetters } from 'vuex';
import { Banner } from '@components/Banner';
import Loading from '@shell/components/Loading.vue';
import AsyncButton from '@shell/components/AsyncButton.vue';

import type { JourneyStep, JourneyAction, JourneyContext } from '../../types/journey';

interface ExecutionLog {
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
}

interface NextAction {
  id: string;
  label: string;
  action: JourneyAction;
}

export default defineComponent({
  name: 'WorkflowIntegration',

  components: {
    Banner,
    Loading,
    AsyncButton,
  },

  props: {
    step: {
      type: Object as () => JourneyStep,
      required: true,
    },
    action: {
      type: Object as () => JourneyAction,
      default: null,
    },
    context: {
      type: Object as () => JourneyContext,
      required: true,
    },
    allowSkip: {
      type: Boolean,
      default: true,
    },
    // External workflow state control for demo purposes
    workflowState: {
      type: String as () => 'ready' | 'executing' | 'completed' | 'error',
      default: null,
    },
    progress: {
      type: Number,
      default: 0,
    },
    executionLogs: {
      type: Array as () => ExecutionLog[],
      default: () => [],
    },
  },

  emits: [
    'workflow-started',
    'workflow-completed',
    'workflow-error',
    'workflow-skipped',
    'demo-auto-progress',
  ],

  setup(props, context) {
    const internalWorkflowState = ref<'ready' | 'executing' | 'completed' | 'error'>('ready');
    const isLoading = ref(false);
    const internalProgress = ref(0);
    const progressText = ref('');
    const currentExecutionStep = ref('');
    const internalExecutionLogs = ref<ExecutionLog[]>([]);
    const executionResult = ref<any>(null);
    const errorMessage = ref('');
    const errorDetails = ref('');
    const completionMessage = ref('');
    const nextActions = ref<NextAction[]>([]);

    // Use external props when provided (for demo mode), otherwise use internal state
    const workflowState = computed(() => props.workflowState || internalWorkflowState.value);
    const progress = computed(() => props.progress !== undefined ? props.progress : internalProgress.value);
    const executionLogs = computed(() => props.executionLogs?.length ? props.executionLogs : internalExecutionLogs.value);

    // Demo mode detection
    const isDemoMode = computed(() => !!props.workflowState);

    // Auto-progression timer for demo mode
    const demoTimer = ref<NodeJS.Timeout | null>(null);

    // Watch for external workflow state changes and update demo data accordingly
    watch(() => props.workflowState, (newState) => {
      if (newState) {
        updateDemoDataForState(newState);
      }
    }, { immediate: true });

    function updateDemoDataForState(state: string) {
      // Clear any existing timer
      if (demoTimer.value) {
        clearTimeout(demoTimer.value);
        demoTimer.value = null;
      }

      switch (state) {
        case 'ready':
          internalProgress.value = 0;
          internalExecutionLogs.value = [];
          errorMessage.value = '';
          errorDetails.value = '';
          completionMessage.value = '';
          currentExecutionStep.value = '';
          break;
        case 'executing':
          internalProgress.value = 45;
          currentExecutionStep.value = 'Creating cloud credential...';
          internalExecutionLogs.value = [
            { level: 'info', message: 'Starting credential creation...', timestamp: new Date() },
            { level: 'info', message: 'Validating AWS credentials...', timestamp: new Date() },
            { level: 'info', message: 'Creating credential resource...', timestamp: new Date() },
          ];

          // Auto-progress to completed state after 3 seconds in demo mode
          if (isDemoMode.value) {
            demoTimer.value = setTimeout(() => {
              // Emit event to parent to change state to completed
              // This allows the demo to continue automatically
              console.log('Demo: Auto-progressing from executing to completed');
              // Emit the auto-progression event to the parent
              context.emit('demo-auto-progress', 'completed');
            }, 3000);
          }
          break;
        case 'completed':
          internalProgress.value = 100;
          completionMessage.value = 'Cloud credential created successfully!';
          currentExecutionStep.value = 'Completed';
          internalExecutionLogs.value = [
            { level: 'info', message: 'Starting credential creation...', timestamp: new Date() },
            { level: 'info', message: 'Validating AWS credentials...', timestamp: new Date() },
            { level: 'info', message: 'Creating credential resource...', timestamp: new Date() },
            { level: 'success', message: 'Credential created successfully!', timestamp: new Date() },
          ];
          executionResult.value = {
            credentialId: 'demo-aws-credential',
            provider: 'aws',
            region: 'us-west-2',
            status: 'active'
          };
          break;
        case 'error':
          internalProgress.value = 30;
          errorMessage.value = 'Failed to create cloud credential';
          errorDetails.value = 'Invalid AWS access key provided. Please check your credentials and try again.';
          currentExecutionStep.value = 'Failed';
          internalExecutionLogs.value = [
            { level: 'info', message: 'Starting credential creation...', timestamp: new Date() },
            { level: 'info', message: 'Validating AWS credentials...', timestamp: new Date() },
            { level: 'error', message: 'Invalid AWS access key provided', timestamp: new Date() },
          ];
          break;
      }
    }

    return {
      workflowState,
      internalWorkflowState,
      isLoading,
      progress,
      internalProgress,
      progressText,
      currentExecutionStep,
      executionLogs,
      internalExecutionLogs,
      executionResult,
      errorMessage,
      errorDetails,
      completionMessage,
      nextActions,
      updateDemoDataForState,
      isDemoMode,
      demoTimer,
    };
  },

  computed: {
    ...mapGetters({
      t: 'i18n/t',
    }),

    statusClass(): string {
      switch (this.workflowState) {
        case 'ready':
          return 'status-ready';
        case 'executing':
          return 'status-executing';
        case 'completed':
          return 'status-completed';
        case 'error':
          return 'status-error';
        default:
          return '';
      }
    },

    statusIcon(): string {
      switch (this.workflowState) {
        case 'ready':
          return 'icon-play';
        case 'executing':
          return 'icon-spinner icon-spin';
        case 'completed':
          return 'icon-checkmark';
        case 'error':
          return 'icon-warning';
        default:
          return 'icon-info';
      }
    },

    statusText(): string {
      switch (this.workflowState) {
        case 'ready':
          return this.t('journey.workflow.status.ready');
        case 'executing':
          return this.t('journey.workflow.status.executing');
        case 'completed':
          return this.t('journey.workflow.status.completed');
        case 'error':
          return this.t('journey.workflow.status.error');
        default:
          return '';
      }
    },

    canExecute(): boolean {
      // In demo mode, don't allow execution
      if (this.workflowState !== this.internalWorkflowState) {
        return false;
      }
      return this.workflowState === 'ready' && !!this.action;
    },

    actionParams(): Record<string, any> | null {
      return this.action?.params || this.action?.payload || null;
    },
  },

  methods: {
    async executeWorkflow() {
      if (!this.action || !this.canExecute) {
        return;
      }

      // Don't execute if we're in demo mode (external workflow state provided)
      if (this.workflowState !== this.internalWorkflowState) {
        return;
      }

      try {
        this.internalWorkflowState = 'executing';
        this.isLoading = true;
        this.internalProgress = 0;
        this.internalExecutionLogs = [];
        this.errorMessage = '';
        this.errorDetails = '';

        this.addLog('info', this.t('journey.workflow.log.starting'));
        this.$emit('workflow-started', { step: this.step, action: this.action });

        // Execute the workflow action
        const result = await this.executeAction();

        this.internalWorkflowState = 'completed';
        this.executionResult = result;
        this.completionMessage = this.t('journey.workflow.log.completed');
        this.addLog('success', this.completionMessage);

        // Generate next actions if applicable
        this.generateNextActions(result);

        this.$emit('workflow-completed', {
          step: this.step,
          action: this.action,
          result,
          nextActions: this.nextActions
        });

      } catch (error) {
        this.internalWorkflowState = 'error';
        this.errorMessage = error.message || this.t('journey.workflow.log.failed');
        this.errorDetails = error.stack || JSON.stringify(error, null, 2);
        this.addLog('error', this.errorMessage);

        this.$emit('workflow-error', {
          step: this.step,
          action: this.action,
          error
        });
      } finally {
        this.isLoading = false;
        this.internalProgress = 100;
      }
    },

    async executeAction(): Promise<any> {
      if (!this.action) {
        throw new Error('No action defined');
      }

      this.updateProgress(10, this.t('journey.workflow.log.preparingAction'));

      switch (this.action.type) {
        case 'navigate':
          return this.executeNavigateAction();
        case 'create-resource':
          return this.executeCreateResourceAction();
        case 'update-resource':
          return this.executeUpdateResourceAction();
        case 'trigger-workflow':
          return this.executeTriggerWorkflowAction();
        case 'show-modal':
          return this.executeShowModalAction();
        case 'custom':
          return this.executeCustomAction();
        default:
          throw new Error(`Unknown action type: ${this.action.type}`);
      }
    },

    async executeNavigateAction(): Promise<any> {
      this.updateProgress(50, this.t('journey.workflow.log.navigating'));
      
      if (!this.action?.target) {
        throw new Error('Navigation target not specified');
      }

      // Navigate to the specified route
      await this.$router.push({
        name: this.action.target,
        params: this.action.params || {},
        query: this.action.payload || {},
      });

      this.updateProgress(100, this.t('journey.workflow.log.navigationComplete'));
      return { navigated: true, target: this.action.target };
    },

    async executeCreateResourceAction(): Promise<any> {
      this.updateProgress(30, this.t('journey.workflow.log.creatingResource'));
      
      // TODO: Implement resource creation logic
      // This would integrate with Rancher's resource creation APIs
      
      this.updateProgress(100, this.t('journey.workflow.log.resourceCreated'));
      return { created: true, resource: this.action.target };
    },

    async executeUpdateResourceAction(): Promise<any> {
      this.updateProgress(30, this.t('journey.workflow.log.updatingResource'));
      
      // TODO: Implement resource update logic
      
      this.updateProgress(100, this.t('journey.workflow.log.resourceUpdated'));
      return { updated: true, resource: this.action.target };
    },

    async executeTriggerWorkflowAction(): Promise<any> {
      this.updateProgress(30, this.t('journey.workflow.log.triggeringWorkflow'));
      
      // TODO: Implement workflow triggering logic
      
      this.updateProgress(100, this.t('journey.workflow.log.workflowTriggered'));
      return { triggered: true, workflow: this.action.target };
    },

    async executeShowModalAction(): Promise<any> {
      this.updateProgress(50, this.t('journey.workflow.log.showingModal'));
      
      // TODO: Implement modal showing logic
      
      this.updateProgress(100, this.t('journey.workflow.log.modalShown'));
      return { shown: true, modal: this.action.target };
    },

    async executeCustomAction(): Promise<any> {
      this.updateProgress(30, this.t('journey.workflow.log.executingCustom'));
      
      if (this.action?.customHandler) {
        const result = await this.action.customHandler(this.context);
        this.updateProgress(100, this.t('journey.workflow.log.customComplete'));
        return result;
      }
      
      throw new Error('Custom action handler not defined');
    },

    retryWorkflow() {
      // Only allow retry if not in demo mode
      if (this.workflowState === this.internalWorkflowState) {
        this.internalWorkflowState = 'ready';
        this.errorMessage = '';
        this.errorDetails = '';
        this.internalExecutionLogs = [];
      }
    },

    skipWorkflow() {
      this.$emit('workflow-skipped', { step: this.step, action: this.action });
    },

    executeNextAction(nextAction: NextAction) {
      // TODO: Execute the next action
      console.log('Executing next action:', nextAction);
    },

    updateProgress(percent: number, text: string) {
      this.internalProgress = percent;
      this.progressText = text;
      this.currentExecutionStep = text;
      this.addLog('info', text);
    },

    addLog(level: ExecutionLog['level'], message: string) {
      this.internalExecutionLogs.push({
        timestamp: new Date(),
        level,
        message,
      });
    },

    formatTimestamp(timestamp: Date): string {
      return timestamp.toLocaleTimeString();
    },

    getActionTypeLabel(): string {
      if (!this.action) return '';
      
      const typeKey = `journey.workflow.actionTypes.${this.action.type}`;
      return this.t(typeKey, {}, false) || this.action.type;
    },

    getExecuteButtonLabel(): string {
      if (!this.action) return this.t('journey.workflow.execute');
      
      const labelKey = `journey.workflow.executeLabels.${this.action.type}`;
      return this.t(labelKey, {}, false) || this.t('journey.workflow.execute');
    },

    generateNextActions(result: any) {
      // TODO: Generate contextual next actions based on the execution result
      this.nextActions = [];
    },
  },
});
</script>

<style lang="scss" scoped>
.workflow-integration {
  max-width: 800px;
  margin: 0 auto;

  .workflow-header {
    text-align: center;
    margin-bottom: 30px;

    .workflow-title {
      margin-bottom: 15px;
      color: var(--primary-text);
      font-size: 1.5rem;
    }

    .workflow-description {
      color: var(--body-text);
      line-height: 1.6;
    }
  }

  .workflow-status {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--box-bg);
    border-radius: 6px;
    border: 1px solid var(--border);

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;

      .status-text {
        font-weight: 600;
        font-size: 1.1rem;
      }

      &.status-ready {
        color: var(--info);
      }

      &.status-executing {
        color: var(--warning);
      }

      &.status-completed {
        color: var(--success);
      }

      &.status-error {
        color: var(--error);
      }
    }

    .status-progress {
      .progress-bar {
        width: 100%;
        height: 8px;
        background: var(--border);
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 8px;

        .progress-fill {
          height: 100%;
          background: var(--primary);
          transition: width 0.3s ease;
        }
      }

      .progress-text {
        font-size: 0.9rem;
        color: var(--body-text);
      }
    }
  }

  .workflow-content {
    .action-preview {
      margin-bottom: 20px;
      padding: 20px;
      background: var(--box-bg);
      border-radius: 6px;
      border: 1px solid var(--border);

      h4 {
        margin-bottom: 15px;
        color: var(--primary-text);
      }

      .action-details {
        .action-type,
        .action-target {
          margin-bottom: 10px;
          color: var(--body-text);
        }

        .action-params {
          margin-top: 15px;

          pre {
            background: var(--code-bg);
            padding: 10px;
            border-radius: 4px;
            font-size: 0.85rem;
            overflow-x: auto;
          }
        }
      }
    }

    .workflow-actions {
      display: flex;
      justify-content: center;
      gap: 10px;
    }

    .execution-status {
      text-align: center;
      margin-bottom: 20px;

      h4 {
        margin: 15px 0 10px 0;
        color: var(--primary-text);
      }

      p {
        color: var(--body-text);
      }
    }

    .demo-spinner {
      display: inline-block;
      margin-bottom: 10px;

      .icon {
        color: var(--primary);
      }
    }

    .execution-logs {
      margin-top: 20px;

      h5 {
        margin-bottom: 10px;
        color: var(--primary-text);
      }

      .log-container {
        max-height: 200px;
        overflow-y: auto;
        background: var(--code-bg);
        border-radius: 4px;
        padding: 10px;

        .log-entry {
          display: flex;
          gap: 10px;
          margin-bottom: 5px;
          font-size: 0.85rem;

          .log-timestamp {
            color: var(--muted);
            flex-shrink: 0;
          }

          .log-message {
            flex: 1;
          }

          &.info {
            color: var(--info);
          }

          &.warning {
            color: var(--warning);
          }

          &.error {
            color: var(--error);
          }

          &.success {
            color: var(--success);
          }
        }
      }
    }

    .completion-status,
    .error-status {
      text-align: center;
      margin-bottom: 20px;

      h4 {
        margin: 15px 0 10px 0;
      }
    }

    .execution-result {
      margin-bottom: 20px;
      padding: 15px;
      background: var(--box-bg);
      border-radius: 6px;
      border: 1px solid var(--border);

      h5 {
        margin-bottom: 10px;
        color: var(--primary-text);
      }

      .result-content {
        pre {
          background: var(--code-bg);
          padding: 10px;
          border-radius: 4px;
          font-size: 0.85rem;
          overflow-x: auto;
        }
      }
    }

    .next-actions {
      margin-top: 20px;

      h5 {
        margin-bottom: 15px;
        color: var(--primary-text);
      }

      .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    }

    .error-actions {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-top: 20px;
    }
  }

  .workflow-additional-content {
    margin-top: 30px;
    padding: 20px;
    background: var(--box-bg);
    border-radius: 6px;
    border: 1px solid var(--border);
  }
}
</style>
